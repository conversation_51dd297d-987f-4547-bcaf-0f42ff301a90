<?php

namespace App\Http\Controllers;

use App\Http\Requests\RechargeRequest;
use App\Models\Covoiturage;
use App\Models\Voiture;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use App\Models\Confirmation;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /** Dashboard utilisateur */
    public function index(Request $request): View
    {
        $user = $request->user();
        $voitures = Voiture::where('user_id', $user->user_id)->get();

        // Covoiturages proposés par l'utilisateur
        $covoiturages = Covoiturage::with('voiture')
            ->where('user_id', $user->user_id)
            ->where('trip_completed', 0)
            ->where('cancelled', 0)
            ->whereHas('voiture')
            ->orderBy('departure_date', 'asc')
            ->orderBy('departure_time', 'asc')
            ->get();

        // Covoiturages réservés par l'utilisateur
        $reservations = Confirmation::with(['covoiturage.user', 'covoiturage.voiture'])
            ->where('user_id', $user->user_id)
            ->whereHas('covoiturage', function ($q) {
                $q->where('trip_completed', 0)->where('cancelled', 0);
            })
            ->get()
            ->unique('covoit_id');

        return view('dashboard', [
            'user' => $user,
            'voitures' => $voitures,
            'covoiturages' => $covoiturages,
            'reservations' => $reservations,
        ]);
    }

    public function getUpcomingTripDetails(Confirmation $confirmation): JsonResponse
    {
        // Sécurité : vérifier que l'utilisateur connecté est bien le propriétaire de cette confirmation
        if (Auth::id() !== $confirmation->user_id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // On récupère le covoiturage associé à cette confirmation
        $covoiturage = $confirmation->covoiturage;

        // On charge les relations pour être sûr qu'elles sont disponibles
        $covoiturage->load(['user', 'voiture']);

        $driver = $covoiturage->user;
        $car = $covoiturage->voiture;

        if (!$driver || !$car) {
            return response()->json(['error' => 'Impossible de charger les détails du conducteur ou de la voiture.'], 500);
        }

        // On compte le nombre de places que l'utilisateur a réservé pour ce trajet
        $reservedSeats = $covoiturage->confirmations()->where('user_id', Auth::id())->count();

        $recapText = "Nous vous rappelons que vous avez acté votre participation à ce trajet. Vous avez réservé {$reservedSeats} place(s) pour le covoiturage du " .
            Carbon::parse($covoiturage->departure_date)->format('d/m/Y') . " à " .
            Carbon::parse($covoiturage->departure_time)->format('H:i') . ", de " .
            $covoiturage->departure_address . " vers " .
            $covoiturage->arrival_address . ".";

        return response()->json([
            'user_name' => Auth::user()->name,
            'recap_text' => $recapText,
            'trip' => [
                'departure_date' => Carbon::parse($covoiturage->departure_date)->format('d/m/Y'),
                'departure_time' => Carbon::parse($covoiturage->departure_time)->format('H:i'),
                'arrival_date' => Carbon::parse($covoiturage->arrival_date)->format('d/m/Y'),
                'arrival_time' => Carbon::parse($covoiturage->arrival_time)->format('H:i'),
                'departure_address' => $covoiturage->departure_address,
                'arrival_address' => $covoiturage->arrival_address,
            ],
            'driver' => [
                'name' => $driver->name,
                'photo' => $driver->photo ? 'data:' . $driver->phototype . ';base64,' . base64_encode($driver->photo) : null,
                'average_rating' => $driver->averageRating(),
                'total_ratings' => $driver->totalRatings(),
            ],
            'car' => [
                'brand' => $car->brand,
                'model' => $car->model,
                'color' => $car->color,
                'energy' => $car->energie,
            ]
        ]);
    }

    public function recharge(RechargeRequest $request): JsonResponse
    {
        $user = Auth::user();
        $user->n_credit += (int) $request->input('amount');
        $user->save();

        return response()->json([
            'success' => true,
            'new_balance' => $user->n_credit
        ]);
    }
}
