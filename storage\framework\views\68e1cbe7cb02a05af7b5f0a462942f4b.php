<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            <?php echo e(__('Mon Espace')); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <?php
                $userRole = Auth::user()->role;
            ?>

            <!-- Grand écran -->
            <div class="hidden md:grid md:grid-cols-3 gap-6">
                <!--Blocs Profil et Rôle -->
                <div class="md:col-span-2 md:row-span-1">
                    <?php echo $__env->make('dashboard.partials.profil', ['user' => $user], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>
                <div class="md:col-start-3 md:row-span-2 h-full flex flex-col">
                    <div class="flex-grow h-full">
                        <?php echo $__env->make('dashboard.partials.role', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>
                </div>

                <!--Blocs CHAUFFEUR -->
                <?php if($userRole === 'Conducteur'): ?>
                    <div class="md:col-span-2 md:row-start-2">
                        <?php echo $__env->make('dashboard.partials.preferences-conducteur', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>
                    <div class="md:col-span-3 md:row-start-3">
                        <?php echo $__env->make('dashboard.partials.covoiturages-proposes', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>
                    <div class="md:col-span-3 md:row-start-4">
                        <?php echo $__env->make('dashboard.partials.mes-vehicules', ['voitures' => $voitures], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>
                <?php endif; ?>

                <!--Blocs PASSAGER -->
                <?php if($userRole === 'Passager'): ?>
                    <div class="md:col-span-2 md:row-start-2">
                        <?php echo $__env->make('dashboard.partials.reservations', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>
                <?php endif; ?>

                <!--Blocs LES DEUX -->
                <?php if($userRole === 'Les deux'): ?>
                    <div class="md:col-span-2 md:row-start-2">
                        <?php echo $__env->make('dashboard.partials.preferences-conducteur', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>
                    <div class="md:col-span-3 md:row-start-3">
                        <?php echo $__env->make('dashboard.partials.reservations', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>
                    <div class="md:col-span-3 md:row-start-4">
                        <?php echo $__env->make('dashboard.partials.covoiturages-proposes', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>
                    <div class="md:col-span-3 md:row-start-5">
                        <?php echo $__env->make('dashboard.partials.mes-vehicules', ['voitures' => $voitures], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>
                <?php endif; ?>

                <!--Blocs HISTORIQUE -->
                <div class="md:col-span-3">
                    <?php echo $__env->make('dashboard.partials.historique', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>
            </div>

            <!-- Petit écran -->
            <div class="md:hidden space-y-6">
                <?php echo $__env->make('dashboard.partials.profil', ['user' => $user], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                <?php echo $__env->make('dashboard.partials.role', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                <?php if($userRole === 'Conducteur' || $userRole === 'Les deux'): ?>
                    <?php echo $__env->make('dashboard.partials.preferences-conducteur', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                <?php endif; ?>

                <?php if($userRole === 'Passager' || $userRole === 'Les deux'): ?>
                    <?php echo $__env->make('dashboard.partials.reservations', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                <?php endif; ?>

                <?php if($userRole === 'Conducteur' || $userRole === 'Les deux'): ?>
                    <?php echo $__env->make('dashboard.partials.covoiturages-proposes', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                    <?php echo $__env->make('dashboard.partials.mes-vehicules', ['voitures' => $voitures], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                <?php endif; ?>

                <?php echo $__env->make('dashboard.partials.historique', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>
        </div>
    </div>

    <!-- Les modals -->
    <?php echo $__env->make('dashboard.partials.popup', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('dashboard.partials.driverinfo-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('dashboard.partials.edit-preferences-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('dashboard.partials.add-vehicle-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('dashboard.partials.edit-vehicle-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('dashboard.partials.addcovoit-addvehicle-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('dashboard.partials.delete-last-vehicle-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('dashboard.partials.confirm-delete-vehicule-with-covoit-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('dashboard.partials.confirm-delete-all-for-change-role-to-passenger-blade', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('dashboard.partials.create-covoit-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('dashboard.partials.modif-covoit-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('dashboard.partials.covoiturage-avenir-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- Recharge Modal -->
    <div id="recharge-modal" data-recharge-url="<?php echo e(route('credits.recharge')); ?>"
        class="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4" onclick="event.stopPropagation()">
            <!-- Header -->
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-2xl font-bold text-gray-800">Recharger vos crédits</h2>
                <button onclick="closeModal('recharge-modal')"
                    class="text-gray-500 hover:text-gray-800 text-3xl leading-none">&times;</button>
            </div>

            <!-- Body -->
            <div>
                <p class="text-slate-600 mb-4">Sélectionnez le montant à recharger :</p>
                <div id="recharge-amount-options" class="grid grid-cols-3 sm:grid-cols-5 gap-4 mb-6">
                    <?php $__currentLoopData = [10, 20, 50, 100, 200]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $amount): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <label
                            class="credit-option border-2 border-slate-200 rounded-lg p-3 text-center cursor-pointer hover:border-[#2ecc71] hover:bg-green-50 transition-all duration-200">
                            <input type="radio" name="recharge_amount" value="<?php echo e($amount); ?>" class="hidden">
                            <span class="text-xl font-bold text-gray-700"><?php echo e($amount); ?></span>
                            <span class="text-xs text-slate-500 block">crédits</span>
                        </label>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                <div id="payment-warning"
                    class="hidden bg-[#3b82f6] text-[#f1f8e9] p-3 my-4 rounded-lg text-sm text-center" role="alert">
                    <p><i class="fas fa-info-circle mr-2"></i>Ceci est une version TEST du projet ! Pour recharger votre
                        crédit, sélectionnez juste un montant et validez juste le paiement.</p>
                </div>

                <!-- Faux formulaire de paiement -->
                <div class="space-y-3 mt-4">
                    <input type="text" placeholder="Nom"
                        class="w-full p-2 border border-slate-300 rounded-md bg-slate-100 cursor-not-allowed" readonly>
                    <input type="text" placeholder="Prénom"
                        class="w-full p-2 border border-slate-300 rounded-md bg-slate-100 cursor-not-allowed" readonly>
                    <input type="text" placeholder="Numéro de carte de crédit"
                        class="w-full p-2 border border-slate-300 rounded-md bg-slate-100 cursor-not-allowed" readonly>
                    <div class="flex gap-4">
                        <input type="text" placeholder="MM/AA"
                            class="flex-1 p-2 border border-slate-300 rounded-md bg-slate-100 cursor-not-allowed"
                            readonly>
                        <input type="text" placeholder="CVC"
                            class="flex-1 p-2 border border-slate-300 rounded-md bg-slate-100 cursor-not-allowed"
                            readonly>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="mt-6 flex justify-end space-x-4">
                <button type="button" onclick="closeModal('recharge-modal')"
                    class="px-4 py-2 text-sm font-semibold text-white bg-slate-500 rounded-lg hover:bg-slate-600 transition-colors duration-300">Annuler</button>
                <button id="validate-payment-btn" disabled
                    class="px-4 py-2 bg-[#2ecc71] text-white font-semibold rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 shadow-lg transition-all duration-300 disabled:bg-slate-300 disabled:cursor-not-allowed disabled:shadow-none hover:bg-[#27ae60]">Valider
                    le paiement</button>
            </div>
        </div>
    </div>

    <?php $__env->startPush('scripts'); ?>
        <script>
            // Ouvrir et fermer une modale
            function openModal(modalId) {
                const modal = document.getElementById(modalId);
                if (modal) {
                    modal.classList.remove('hidden');
                    document.body.classList.add('overflow-hidden');
                }
            }

            function closeModal(modalId) {
                const modal = document.getElementById(modalId);
                if (modal) {
                    modal.classList.add('hidden');
                    document.body.classList.remove('overflow-hidden');
                }
            }

            // Autres fonctions globales
            window.validateImmat = function(immat) {
                if (!immat) return false;
                const immatUpper = immat.toUpperCase();
                const sivRegex = /^[A-Z]{2}[- ]?\d{3}[- ]?[A-Z]{2}$/;
                const fniRegex = /^\d{1,4}[- ]?[A-Z]{1,3}[- ]?(\d{2}|2[AB])$/;
                return sivRegex.test(immatUpper) || fniRegex.test(immatUpper);
            }

            window.showSuccessNotification = function(message) {
                const notification = document.createElement('div');
                notification.className =
                    'fixed bottom-5 right-5 bg-green-500 text-white py-3 px-5 rounded-lg shadow-xl animate-bounce';
                notification.textContent = message;
                document.body.appendChild(notification);
                setTimeout(() => notification.remove(), 3000);
            }



            window.confirmVehicleDeletion = function(event, vehicleCount) {
                event.preventDefault();
                let formToSubmit = event.target;
                if (vehicleCount > 1) {
                    if (confirm('Êtes-vous sûr de vouloir supprimer ce véhicule ?')) {
                        formToSubmit.submit();
                    }
                } else {
                    openModal('delete-last-vehicle-modal');
                    document.getElementById('confirm-delete-last-vehicle-btn').onclick = function() {
                        formToSubmit.submit();
                    };
                }
                return false;
            }


            document.addEventListener('DOMContentLoaded', function() {
                // Modale pour recharger le crédit
                const rechargeBtn = document.querySelector('.recharge-btn');
                if (rechargeBtn) {
                    rechargeBtn.addEventListener('click', function() {
                        const modalId = this.dataset.modalTarget;
                        if (modalId) {
                            openModal(modalId);
                        }
                    });
                }

                const rechargeModal = document.getElementById('recharge-modal');
                if (rechargeModal) {
                    const creditOptions = document.querySelectorAll('.credit-option');
                    const validateBtn = document.getElementById('validate-payment-btn');
                    const rechargeUrl = rechargeModal.dataset.rechargeUrl;
                    let selectedAmount = null;

                    // Sélection d'un montant
                    creditOptions.forEach(option => {
                        option.addEventListener('click', function() {
                            creditOptions.forEach(opt => opt.classList.remove('border-[#2ecc71]',
                                'bg-green-50', 'ring-2', 'ring-green-300'));
                            this.classList.add('border-[#2ecc71]', 'bg-green-50', 'ring-2',
                                'ring-green-300');
                            selectedAmount = this.querySelector('input[name="recharge_amount"]').value;
                            validateBtn.disabled = false; // Active le bouton
                        });
                    });

                    // Validation du paiement
                    validateBtn.addEventListener('click', function() {
                        if (selectedAmount) {
                            fetch(rechargeUrl, {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json',
                                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')
                                            .getAttribute('content')
                                    },
                                    body: JSON.stringify({
                                        amount: selectedAmount
                                    })
                                })
                                .then(response => response.json())
                                .then(data => {
                                    if (data.success) {
                                        document.querySelectorAll('.credit-balance').forEach(el => el
                                            .textContent = data.new_balance);
                                        closeModal('recharge-modal');
                                        showSuccessNotification('Crédits rechargés avec succès !');
                                    } else {
                                        alert('Une erreur est survenue lors de la recharge.');
                                    }
                                })
                                .catch(() => alert('Une erreur réseau est survenue.'));
                        }
                    });

                    // Afficher l'info quand on clic sur les readonly
                    const readonlyInputs = rechargeModal.querySelectorAll('input[readonly]');
                    const paymentWarning = document.getElementById('payment-warning');

                    readonlyInputs.forEach(input => {
                        input.addEventListener('click', function() {
                            if (paymentWarning) {
                                paymentWarning.classList.remove('hidden');
                            }
                        });
                    });

                    // Réinit pour recharge-modal
                    window.resetRechargeModal = function() {
                        // Désélectionner les options de crédit
                        creditOptions.forEach(opt => opt.classList.remove('border-[#2ecc71]', 'bg-green-50',
                            'ring-2', 'ring-green-300'));

                        // Réinit la variable selectedAmount
                        selectedAmount = null;

                        // Désactive le btn validation
                        validateBtn.disabled = true;

                        // Cache le message d'avertisement
                        if (paymentWarning) {
                            paymentWarning.classList.add('hidden');
                        }
                    }
                }

                // Logique pour les covoit cards
                const covoiturageCards = document.querySelectorAll('.covoiturage-card');
                covoiturageCards.forEach(card => {
                    const tripToggles = card.querySelector('.trip-status-toggle');
                    const modifierBtn = card.querySelector('button[onclick^="openModifModal"]');
                    const annulerForm = card.querySelector('form input[name="_method"][value="DELETE"]')
                        ?.closest('form');
                    const startBtn = card.querySelector('.start-trip-btn');
                    const endBtn = card.querySelector('.end-trip-btn');

                    if (!tripToggles || !modifierBtn || !annulerForm || !startBtn || !endBtn) return;

                    // On annule l'anciennne logique SIMPLE du btn "Annuler" (avec onsubmit) pour lui attribuer deux comportements différents
                    annulerForm.removeAttribute('onsubmit');

                    tripToggles.addEventListener('click', function(event) {
                        const buttonClicked = event.target.closest('button');
                        if (!buttonClicked) return;

                        // Si clic sur "Démarrer" => btn "Modifier" est désactiver + btn "Démarrer" est caché + btn "Vous êtes arrivé ?" est visible
                        if (buttonClicked === startBtn) {
                            modifierBtn.disabled = true;
                            modifierBtn.classList.add('opacity-50', 'cursor-not-allowed');
                            startBtn.classList.add('hidden');
                            endBtn.classList.remove('hidden');
                            card.dataset.tripStarted = 'true';
                        } else if (buttonClicked === endBtn) {
                            // Si clic sur "Vous êtes arrivé ?"...
                            // TODO: la vraie logique!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
                            // Pour le moment=> on grise la card + on désactive les btns + on change le texte du btn "Vous êtes arrivé ?" en "Terminé"
                            card.style.opacity = '0.6';
                            card.style.pointerEvents = 'none';
                            card.querySelectorAll('.card-footer .action-btn').forEach(btn => btn
                                .disabled = true);
                            buttonClicked.textContent = 'Terminé';
                        }
                    });

                    // Si clic sur "Annuler" => on check si le trajet a commencé ou pas. Si oui => on réinit la card. Si non => on demande confirmation de suppression
                    annulerForm.addEventListener('submit', function(event) {
                        event.preventDefault();
                        if (card.dataset.tripStarted === 'true') {
                            modifierBtn.disabled = false;
                            modifierBtn.classList.remove('opacity-50', 'cursor-not-allowed');
                            startBtn.classList.remove('hidden');
                            endBtn.classList.add('hidden');
                            delete card.dataset.tripStarted;
                        } else {
                            if (confirm('Êtes-vous sûr de vouloir annuler ce trajet ?')) {
                                annulerForm.submit();
                            }
                        }
                    });
                });

                // Logique pour la modale des covoiturages à venir
                const upcomingTripModal = document.getElementById('covoiturage-avenir-modal');
                if (upcomingTripModal) {
                    const reservationCards = document.querySelectorAll('.reservation-card');
                    const loadingDiv = document.getElementById('modal-avenir-loading');
                    const contentDiv = document.getElementById('modal-avenir-content');
                    const closeButtons = upcomingTripModal.querySelectorAll('.modal-close');

                    reservationCards.forEach(card => {
                        card.addEventListener('click', function() {
                            const confirmationId = this.dataset.confirmationId;
                            openModal('covoiturage-avenir-modal');
                            loadingDiv.style.display = 'flex';
                            contentDiv.style.display = 'none';

                            fetch(`/api/upcoming-trip/${confirmationId}`)
                                .then(response => {
                                    if (!response.ok) {
                                        throw new Error('Network response was not ok');
                                    }
                                    return response.json();
                                })
                                .then(data => {
                                    // Populate modal
                                    document.getElementById('modal-avenir-user-name').textContent =
                                        data.user_name;
                                    document.getElementById('modal-avenir-recap-text').textContent =
                                        data.recap_text;
                                    document.getElementById('modal-avenir-departure-date')
                                        .textContent = data.trip.departure_date;
                                    document.getElementById('modal-avenir-departure-time')
                                        .textContent = data.trip.departure_time;
                                    document.getElementById('modal-avenir-arrival-date')
                                        .textContent = data.trip.arrival_date;
                                    document.getElementById('modal-avenir-arrival-time')
                                        .textContent = data.trip.arrival_time;
                                    document.getElementById('modal-avenir-departure-address')
                                        .textContent = data.trip.departure_address;
                                    document.getElementById('modal-avenir-arrival-address')
                                        .textContent = data.trip.arrival_address;
                                    document.getElementById('modal-avenir-driver-name')
                                        .textContent = data.driver.name;
                                    document.getElementById('modal-avenir-car-brand').textContent =
                                        data.car.brand;
                                    document.getElementById('modal-avenir-car-model').textContent =
                                        data.car.model;
                                    document.getElementById('modal-avenir-car-color').textContent =
                                        data.car.color;
                                    document.getElementById('modal-avenir-car-energy').textContent =
                                        data.car.energy;

                                    const driverPhotoDiv = document.getElementById(
                                        'modal-avenir-driver-photo');
                                    if (data.driver.photo) {
                                        driverPhotoDiv.innerHTML =
                                            `<img src="${data.driver.photo}" alt="Photo de ${data.driver.name}" class="w-16 h-16 rounded-full object-cover border-2 border-green-500">`;
                                    } else {
                                        driverPhotoDiv.innerHTML =
                                            `<i class="fas fa-user text-gray-600 text-xl"></i>`;
                                    }

                                    const ratingDiv = document.getElementById(
                                        'modal-avenir-driver-rating');
                                    let ratingHTML = '';
                                    if (data.driver.average_rating) {
                                        for (let i = 1; i <= 5; i++) {
                                            if (i <= Math.floor(data.driver.average_rating)) {
                                                ratingHTML +=
                                                    '<i class="fas fa-star text-yellow-400"></i>';
                                            } else if (i - 0.5 <= data.driver.average_rating) {
                                                ratingHTML +=
                                                    '<i class="fas fa-star-half-alt text-yellow-400"></i>';
                                            } else {
                                                ratingHTML +=
                                                    '<i class="far fa-star text-gray-300"></i>';
                                            }
                                        }
                                        ratingHTML +=
                                            `<span class="ml-2 text-gray-600">(${parseFloat(data.driver.average_rating).toFixed(1)}/5 sur ${data.driver.total_ratings} avis)</span>`;
                                    } else {
                                        ratingHTML =
                                            '<span class="text-gray-600">Nouveau conducteur</span>';
                                    }
                                    ratingDiv.innerHTML = ratingHTML;

                                    // Show content
                                    loadingDiv.style.display = 'none';
                                    contentDiv.style.display = 'block';
                                })
                                .catch(error => {
                                    console.error('Fetch Error:', error);
                                    alert('Impossible de charger les détails du trajet.');
                                    closeModal('covoiturage-avenir-modal');
                                });
                        });
                    });

                    closeButtons.forEach(button => {
                        button.addEventListener('click', () => closeModal('covoiturage-avenir-modal'));
                    });

                    upcomingTripModal.addEventListener('click', function(event) {
                        if (event.target === upcomingTripModal) {
                            closeModal('covoiturage-avenir-modal');
                        }
                    });
                }
            });
        </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH /var/www/html/resources/views/dashboard.blade.php ENDPATH**/ ?>