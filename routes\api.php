<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DashboardController;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

Route::get('/upcoming-trip/{confirmation}', [DashboardController::class, 'getUpcomingTripDetails'])->middleware('auth:web');

// Route de test pour debug
Route::get('/test-confirmations', function () {
    $confirmations = \App\Models\Confirmation::with('covoiturage')->take(5)->get();
    return response()->json($confirmations);
})->middleware('auth:web');

// Routes pour la modale détails (au clic sur le btn "Détails" d'un covoiturage-card)
Route::get('/trips/{id}/details', [TripDetailsController::class, 'getDetails']);
Route::get('/trips/{tripId}/user-status', [TripDetailsController::class, 'getUserStatus']);
