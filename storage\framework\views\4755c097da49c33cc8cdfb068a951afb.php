<!-- Block du dashboard: covoiturage futur -->
<div class="bg-white rounded-lg shadow-md overflow-hidden transition-all duration-300 hover:shadow-xl">
    <div class="bg-slate-50 px-6 py-4 border-b border-slate-200">
        <h3 class="text-xl font-bold text-[#2c3e50]">Mes covoiturages à venir</h3>
    </div>
    <div class="p-6">
        <?php if($reservations->isNotEmpty()): ?>
            <div class="space-y-4">
                <?php $__currentLoopData = $reservations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reservation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="reservation-card bg-white rounded-lg shadow-lg overflow-hidden flex flex-col sm:flex-row transition-transform duration-300 hover:transform hover:-translate-y-1 hover:shadow-xl border border-slate-200 cursor-pointer" data-covoit-id="<?php echo e($reservation->covoit_id); ?>">
                        <div class="p-4 flex-grow">
                            <div class="flex justify-between items-start mb-2">
                                <div class="text-xl font-bold text-gray-800">
                                    <span><?php echo e($reservation->city_dep); ?></span>
                                    <span class="mx-2 text-gray-400">→</span>
                                    <span><?php echo e($reservation->city_arr); ?></span>
                                </div>
                                <div class="text-lg font-medium text-gray-700">
                                    <i class="fas fa-calendar-alt mr-2 text-[#2ecc71]"></i>
                                    <?php echo e(\Carbon\Carbon::parse($reservation->departure_date)->format('d/m/Y')); ?>

                                </div>
                            </div>
                            <div class="text-sm text-gray-600">
                                <p>Départ à <b><?php echo e(\Carbon\Carbon::parse($reservation->departure_time)->format('H:i')); ?></b> avec <b><?php echo e($reservation->user->name); ?></b></p>
                                <p>Voiture : <?php echo e($reservation->voiture->brand); ?> <?php echo e($reservation->voiture->model); ?></p>
                            </div>
                        </div>
                        <div class="bg-slate-50 p-4 flex flex-col justify-center items-center sm:w-32">
                            <div class="text-2xl font-bold text-[#2ecc71]"><?php echo e($reservation->price); ?></div>
                            <div class="text-sm text-gray-500">crédits</div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php else: ?>
            <div class="text-center text-slate-500">
                <div class="text-5xl mb-4 text-[#2ecc71]">
                    <i class="fas fa-route"></i>
                </div>
                <p class="mb-4 text-lg">Vous n'avez pas encore de trajet réservé.</p>
                <a href="<?php echo e(route('covoiturage')); ?>"
                    class="inline-block px-6 py-2 bg-[#2ecc71] text-white font-semibold rounded-md hover:bg-[#27ae60] shadow-lg transition-all duration-300 transform hover:scale-105">
                    Rechercher un trajet
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php /**PATH /var/www/html/resources/views/dashboard/partials/reservations.blade.php ENDPATH**/ ?>