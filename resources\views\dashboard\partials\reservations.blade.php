<!-- Block du dashboard: covoiturage futur -->
<div class="bg-white rounded-lg shadow-md overflow-hidden transition-all duration-300 hover:shadow-xl">
    <div class="bg-slate-50 px-6 py-4 border-b border-slate-200">
        <h3 class="text-xl font-bold text-[#2c3e50]">Mes covoiturages à venir</h3>
    </div>
    <div class="p-6">
        @if ($reservations->isNotEmpty())
            <div class="space-y-4">
                @foreach ($reservations as $reservation)
                    <div class="reservation-card bg-white rounded-lg shadow-lg overflow-hidden flex flex-col sm:flex-row transition-transform duration-300 hover:transform hover:-translate-y-1 hover:shadow-xl border border-slate-200 cursor-pointer" data-covoit-id="{{ $reservation->covoit_id }}">
                        <div class="p-4 flex-grow">
                            <div class="flex justify-between items-start mb-2">
                                <div class="text-xl font-bold text-gray-800">
                                    <span>{{ $reservation->city_dep }}</span>
                                    <span class="mx-2 text-gray-400">→</span>
                                    <span>{{ $reservation->city_arr }}</span>
                                </div>
                                <div class="text-lg font-medium text-gray-700">
                                    <i class="fas fa-calendar-alt mr-2 text-[#2ecc71]"></i>
                                    {{ \Carbon\Carbon::parse($reservation->departure_date)->format('d/m/Y') }}
                                </div>
                            </div>
                            <div class="text-sm text-gray-600">
                                <p>Départ à <b>{{ \Carbon\Carbon::parse($reservation->departure_time)->format('H:i') }}</b> avec <b>{{ $reservation->user->name }}</b></p>
                                <p>Voiture : {{ $reservation->voiture->brand }} {{ $reservation->voiture->model }}</p>
                            </div>
                        </div>
                        <div class="bg-slate-50 p-4 flex flex-col justify-center items-center sm:w-32">
                            <div class="text-2xl font-bold text-[#2ecc71]">{{ $reservation->price }}</div>
                            <div class="text-sm text-gray-500">crédits</div>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="text-center text-slate-500">
                <div class="text-5xl mb-4 text-[#2ecc71]">
                    <i class="fas fa-route"></i>
                </div>
                <p class="mb-4 text-lg">Vous n'avez pas encore de trajet réservé.</p>
                <a href="{{ route('covoiturage') }}"
                    class="inline-block px-6 py-2 bg-[#2ecc71] text-white font-semibold rounded-md hover:bg-[#27ae60] shadow-lg transition-all duration-300 transform hover:scale-105">
                    Rechercher un trajet
                </a>
            </div>
        @endif
    </div>
</div>
